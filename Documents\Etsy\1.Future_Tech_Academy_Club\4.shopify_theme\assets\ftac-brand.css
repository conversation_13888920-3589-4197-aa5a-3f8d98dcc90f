/* Future Tech Academy Club Brand Design System */

/* Typography - Modern Sans-Serif (Outfit/Inter) */
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Brand Typography */
  --ftac-font-primary: 'Outfit', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --ftac-font-secondary: 'Inter', 'Outfit', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Font Weights */
  --ftac-font-light: 300;
  --ftac-font-regular: 400;
  --ftac-font-medium: 500;
  --ftac-font-semibold: 600;
  --ftac-font-bold: 700;
  
  /* Font Sizes */
  --ftac-text-xs: 0.75rem;    /* 12px */
  --ftac-text-sm: 0.875rem;   /* 14px */
  --ftac-text-base: 1rem;     /* 16px */
  --ftac-text-lg: 1.125rem;   /* 18px */
  --ftac-text-xl: 1.25rem;    /* 20px */
  --ftac-text-2xl: 1.5rem;    /* 24px */
  --ftac-text-3xl: 1.875rem;  /* 30px */
  --ftac-text-4xl: 2.25rem;   /* 36px */
  --ftac-text-5xl: 3rem;      /* 48px */
  --ftac-text-6xl: 3.75rem;   /* 60px */
  
  /* Spacing */
  --ftac-space-1: 0.25rem;    /* 4px */
  --ftac-space-2: 0.5rem;     /* 8px */
  --ftac-space-3: 0.75rem;    /* 12px */
  --ftac-space-4: 1rem;       /* 16px */
  --ftac-space-5: 1.25rem;    /* 20px */
  --ftac-space-6: 1.5rem;     /* 24px */
  --ftac-space-8: 2rem;       /* 32px */
  --ftac-space-10: 2.5rem;    /* 40px */
  --ftac-space-12: 3rem;      /* 48px */
  --ftac-space-16: 4rem;      /* 64px */
  --ftac-space-20: 5rem;      /* 80px */
  --ftac-space-24: 6rem;      /* 96px */
  
  /* Border Radius */
  --ftac-radius-sm: 0.25rem;  /* 4px */
  --ftac-radius-md: 0.5rem;   /* 8px */
  --ftac-radius-lg: 0.75rem;  /* 12px */
  --ftac-radius-xl: 1rem;     /* 16px */
  --ftac-radius-2xl: 1.5rem;  /* 24px */
  
  /* Shadows */
  --ftac-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ftac-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ftac-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --ftac-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Base Typography Classes */
.ftac-font-primary {
  font-family: var(--ftac-font-primary);
}

.ftac-font-secondary {
  font-family: var(--ftac-font-secondary);
}

/* Heading Styles */
.ftac-heading-hero {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-5xl);
  font-weight: var(--ftac-font-bold);
  line-height: 1.1;
  color: var(--ftac-charcoal);
}

.ftac-heading-1 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-4xl);
  font-weight: var(--ftac-font-semibold);
  line-height: 1.2;
  color: var(--ftac-charcoal);
}

.ftac-heading-2 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-3xl);
  font-weight: var(--ftac-font-semibold);
  line-height: 1.3;
  color: var(--ftac-charcoal);
}

.ftac-heading-3 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-2xl);
  font-weight: var(--ftac-font-medium);
  line-height: 1.4;
  color: var(--ftac-charcoal);
}

/* Body Text Styles */
.ftac-text-body {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-base);
  font-weight: var(--ftac-font-regular);
  line-height: 1.6;
  color: var(--ftac-charcoal);
}

.ftac-text-large {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-lg);
  font-weight: var(--ftac-font-regular);
  line-height: 1.6;
  color: var(--ftac-charcoal);
}

.ftac-text-small {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-sm);
  font-weight: var(--ftac-font-regular);
  line-height: 1.5;
  color: var(--ftac-charcoal);
}

/* Button Styles */
.ftac-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--ftac-space-3) var(--ftac-space-6);
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-base);
  font-weight: var(--ftac-font-medium);
  text-decoration: none;
  border: none;
  border-radius: var(--ftac-radius-lg);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 44px; /* Touch-friendly */
}

.ftac-btn-primary {
  background-color: var(--ftac-academy-blue);
  color: white;
  box-shadow: var(--ftac-shadow-md);
}

.ftac-btn-primary:hover {
  background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
  box-shadow: var(--ftac-shadow-lg);
  transform: translateY(-1px);
}

.ftac-btn-secondary {
  background-color: var(--ftac-learning-green);
  color: white;
  box-shadow: var(--ftac-shadow-md);
}

.ftac-btn-secondary:hover {
  background-color: rgba(var(--ftac-learning-green-rgb), 0.9);
  box-shadow: var(--ftac-shadow-lg);
  transform: translateY(-1px);
}

.ftac-btn-outline {
  background-color: transparent;
  color: var(--ftac-academy-blue);
  border: 2px solid var(--ftac-academy-blue);
}

.ftac-btn-outline:hover {
  background-color: var(--ftac-academy-blue);
  color: white;
}

.ftac-btn-large {
  padding: var(--ftac-space-4) var(--ftac-space-8);
  font-size: var(--ftac-text-lg);
  min-height: 52px;
}

/* Card Styles */
.ftac-card {
  background-color: white;
  border-radius: var(--ftac-radius-xl);
  box-shadow: var(--ftac-shadow-md);
  padding: var(--ftac-space-6);
  transition: all 0.2s ease-in-out;
}

.ftac-card:hover {
  box-shadow: var(--ftac-shadow-lg);
  transform: translateY(-2px);
}

.ftac-card-warm {
  background-color: var(--ftac-warm-cream);
}

/* Color Utilities */
.ftac-text-academy-blue { color: var(--ftac-academy-blue); }
.ftac-text-learning-green { color: var(--ftac-learning-green); }
.ftac-text-charcoal { color: var(--ftac-charcoal); }

.ftac-bg-academy-blue { background-color: var(--ftac-academy-blue); }
.ftac-bg-learning-green { background-color: var(--ftac-learning-green); }
.ftac-bg-warm-cream { background-color: var(--ftac-warm-cream); }
.ftac-bg-charcoal { background-color: var(--ftac-charcoal); }

/* Layout Utilities */
.ftac-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--ftac-space-4);
}

.ftac-section {
  padding: var(--ftac-space-16) 0;
}

.ftac-section-large {
  padding: var(--ftac-space-24) 0;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .ftac-heading-hero {
    font-size: var(--ftac-text-4xl);
  }
  
  .ftac-heading-1 {
    font-size: var(--ftac-text-3xl);
  }
  
  .ftac-heading-2 {
    font-size: var(--ftac-text-2xl);
  }
  
  .ftac-heading-3 {
    font-size: var(--ftac-text-xl);
  }
}

/* Trust Elements */
.ftac-trust-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--ftac-space-2) var(--ftac-space-3);
  background-color: var(--ftac-warm-cream);
  color: var(--ftac-charcoal);
  border-radius: var(--ftac-radius-md);
  font-size: var(--ftac-text-sm);
  font-weight: var(--ftac-font-medium);
}

.ftac-testimonial {
  background-color: white;
  border-left: 4px solid var(--ftac-learning-green);
  padding: var(--ftac-space-6);
  border-radius: var(--ftac-radius-lg);
  box-shadow: var(--ftac-shadow-sm);
}

/* Professional Polish */
.ftac-gradient-bg {
  background: linear-gradient(135deg, var(--ftac-academy-blue) 0%, var(--ftac-learning-green) 100%);
}

.ftac-warm-gradient {
  background: linear-gradient(135deg, var(--ftac-warm-cream) 0%, rgba(var(--ftac-warm-cream-rgb), 0.5) 100%);
}
