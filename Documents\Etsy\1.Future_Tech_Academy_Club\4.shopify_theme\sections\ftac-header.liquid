{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-header {
    background-color: white;
    border-bottom: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--ftac-shadow-sm);
  }
  
  .ftac-header__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--ftac-space-4) var(--ftac-space-6);
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .ftac-header__logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--ftac-charcoal);
  }
  
  .ftac-header__logo-text {
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-bold);
    font-size: var(--ftac-text-2xl);
    line-height: 1;
  }
  
  .ftac-header__logo-academy {
    color: var(--ftac-academy-blue);
    font-size: var(--ftac-text-3xl);
  }
  
  .ftac-header__logo-future {
    color: var(--ftac-charcoal);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-medium);
    margin-bottom: var(--ftac-space-1);
  }
  
  .ftac-header__nav {
    display: none;
  }
  
  .ftac-header__nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--ftac-space-8);
  }
  
  .ftac-header__nav-link {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
    text-decoration: none;
    transition: color 0.2s ease;
  }
  
  .ftac-header__nav-link:hover {
    color: var(--ftac-academy-blue);
  }
  
  .ftac-header__nav-link--active {
    color: var(--ftac-academy-blue);
  }
  
  .ftac-header__actions {
    display: flex;
    align-items: center;
    gap: var(--ftac-space-4);
  }
  
  .ftac-header__cta {
    background-color: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-3) var(--ftac-space-6);
    border-radius: var(--ftac-radius-lg);
    text-decoration: none;
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-medium);
    font-size: var(--ftac-text-base);
    transition: all 0.2s ease;
    box-shadow: var(--ftac-shadow-md);
  }
  
  .ftac-header__cta:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
    box-shadow: var(--ftac-shadow-lg);
  }
  
  .ftac-header__mobile-menu {
    display: block;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--ftac-space-2);
  }
  
  .ftac-header__mobile-menu-icon {
    width: 24px;
    height: 24px;
    fill: var(--ftac-charcoal);
  }
  
  /* Desktop Navigation */
  @media (min-width: 768px) {
    .ftac-header__nav {
      display: block;
    }
    
    .ftac-header__mobile-menu {
      display: none;
    }
  }
  
  /* Mobile Menu Overlay */
  .ftac-mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    z-index: 200;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .ftac-mobile-menu.active {
    transform: translateX(0);
  }
  
  .ftac-mobile-menu__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ftac-space-4) var(--ftac-space-6);
    border-bottom: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
  }
  
  .ftac-mobile-menu__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--ftac-space-2);
  }
  
  .ftac-mobile-menu__nav {
    padding: var(--ftac-space-6);
  }
  
  .ftac-mobile-menu__list {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .ftac-mobile-menu__item {
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-mobile-menu__link {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
    text-decoration: none;
  }
  
  .ftac-mobile-menu__cta {
    margin-top: var(--ftac-space-8);
  }
</style>

<header class="ftac-header">
  <div class="ftac-header__container">
    <!-- Logo -->
    <a href="{{ routes.root_url }}" class="ftac-header__logo">
      {%- if settings.logo != blank -%}
        <img src="{{ settings.logo | image_url: width: 200 }}" alt="{{ shop.name }}" style="height: 40px; width: auto;">
      {%- else -%}
        <div class="ftac-header__logo-text">
          <div class="ftac-header__logo-future">Future Tech</div>
          <div class="ftac-header__logo-academy">ACADEMY CLUB</div>
        </div>
      {%- endif -%}
    </a>
    
    <!-- Desktop Navigation -->
    <nav class="ftac-header__nav">
      <ul class="ftac-header__nav-list">
        <li><a href="{{ routes.root_url }}" class="ftac-header__nav-link{% if request.page_type == 'index' %} ftac-header__nav-link--active{% endif %}">Home</a></li>
        <li><a href="/pages/about-sarah" class="ftac-header__nav-link{% if page.handle == 'about-sarah' %} ftac-header__nav-link--active{% endif %}">About Sarah</a></li>
        <li><a href="/pages/contact-support" class="ftac-header__nav-link{% if page.handle == 'contact-support' %} ftac-header__nav-link--active{% endif %}">Contact Support</a></li>
      </ul>
    </nav>
    
    <!-- Actions -->
    <div class="ftac-header__actions">
      <a href="/pages/member-access" class="ftac-header__cta">Access Your Content</a>
      
      <!-- Mobile Menu Button -->
      <button class="ftac-header__mobile-menu" onclick="toggleMobileMenu()">
        <svg class="ftac-header__mobile-menu-icon" viewBox="0 0 24 24">
          <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>
    </div>
  </div>
</header>

<!-- Mobile Menu Overlay -->
<div class="ftac-mobile-menu" id="mobileMenu">
  <div class="ftac-mobile-menu__header">
    <div class="ftac-header__logo-text">
      <div class="ftac-header__logo-future">Future Tech</div>
      <div class="ftac-header__logo-academy">ACADEMY CLUB</div>
    </div>
    <button class="ftac-mobile-menu__close" onclick="toggleMobileMenu()">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
  
  <nav class="ftac-mobile-menu__nav">
    <ul class="ftac-mobile-menu__list">
      <li class="ftac-mobile-menu__item">
        <a href="{{ routes.root_url }}" class="ftac-mobile-menu__link">Home</a>
      </li>
      <li class="ftac-mobile-menu__item">
        <a href="/pages/about-sarah" class="ftac-mobile-menu__link">About Sarah</a>
      </li>
      <li class="ftac-mobile-menu__item">
        <a href="/pages/contact-support" class="ftac-mobile-menu__link">Contact Support</a>
      </li>
    </ul>
    
    <div class="ftac-mobile-menu__cta">
      <a href="/pages/member-access" class="ftac-btn ftac-btn-primary ftac-btn-large" style="width: 100%; text-align: center;">Access Your Content</a>
    </div>
  </nav>
</div>

<script>
  function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    mobileMenu.classList.toggle('active');
    
    // Prevent body scroll when menu is open
    if (mobileMenu.classList.contains('active')) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }
  
  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    const mobileMenu = document.getElementById('mobileMenu');
    const menuButton = document.querySelector('.ftac-header__mobile-menu');
    
    if (!mobileMenu.contains(event.target) && !menuButton.contains(event.target)) {
      mobileMenu.classList.remove('active');
      document.body.style.overflow = '';
    }
  });
</script>

{% schema %}
{
  "name": "FTAC Header",
  "settings": [
    {
      "type": "header",
      "content": "Future Tech Academy Club Header"
    },
    {
      "type": "paragraph",
      "content": "Custom header for Future Tech Academy Club with brand-specific navigation and styling."
    }
  ]
}
{% endschema %}
