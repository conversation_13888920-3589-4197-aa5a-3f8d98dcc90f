{{ 'customer.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="customer reset-password section-{{ section.id }}-padding">
  <h1>
    {{ 'customer.reset_password.title' | t }}
  </h1>
  <p>
    {{ 'customer.reset_password.subtext' | t }}
  </p>
  {%- form 'reset_customer_password' -%}
    {%- if form.errors -%}
      <h2 class="form__message" tabindex="-1" autofocus>
        <span class="visually-hidden">{{ 'accessibility.error' | t }} </span>
        <span class="svg-wrapper">
          {{- 'icon-error.svg' | inline_asset_content -}}
        </span>
        {{ 'templates.contact.form.error_heading' | t }}
      </h2>
      <ul>
        {%- for field in form.errors -%}
          <li>
            {%- if field == 'form' -%}
              {{ form.errors.messages[field] }}
            {%- else -%}
              <a href="#{{ field }}">
                {{ form.errors.translated_fields[field] | capitalize }}
                {{ form.errors.messages[field] }}
              </a>
            {%- endif -%}
          </li>
        {%- endfor -%}
      </ul>
    {%- endif -%}

    <div class="field">
      <input
        type="password"
        name="customer[password]"
        id="password"
        autocomplete="new-password"
        {% if form.errors contains 'password' %}
          aria-invalid="true"
          aria-describedby="password-error"
        {% endif %}
        placeholder="{{ 'customer.reset_password.password' | t }}"
      >
      <label for="password">
        {{ 'customer.reset_password.password' | t }}
      </label>
      {%- if form.errors contains 'password' -%}
        <small id="password-error" class="form__message">
          <span class="svg-wrapper">
            {{- 'icon-error.svg' | inline_asset_content -}}
          </span>
          {{ form.errors.translated_fields.password | capitalize }}
          {{ form.errors.messages.password }}
        </small>
      {%- endif -%}
    </div>

    <div class="field">
      <input
        type="password"
        name="customer[password_confirmation]"
        id="password_confirmation"
        autocomplete="new-password"
        {% if form.errors contains 'password_confirmation' %}
          aria-invalid="true"
          aria-describedby="password_confirmation-error"
        {% endif %}
        placeholder="{{ 'customer.reset_password.password_confirm' | t }}"
      >
      <label for="password_confirmation">
        {{ 'customer.reset_password.password_confirm' | t }}
      </label>
      {%- if form.errors contains 'password_confirmation' -%}
        <small id="password_confirmation-error" class="form__message">
          <span class="svg-wrapper">
            {{- 'icon-error.svg' | inline_asset_content -}}
          </span>
          {{ form.errors.translated_fields.password_confirmation | capitalize }}
          {{ form.errors.messages.password_confirmation }}
        </small>
      {%- endif -%}
    </div>

    <button>
      {{ 'customer.reset_password.submit' | t }}
    </button>
  {%- endform -%}
</div>

{% schema %}
{
  "name": "t:sections.main-reset-password.name",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
