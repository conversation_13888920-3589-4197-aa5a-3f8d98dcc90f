variant-selects {
  display: block;
}

.product--no-media .product-form__input--pill,
.product--no-media .product-form__input--swatch,
.product--no-media .product-form__input--dropdown {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.product--no-media .product-form__input.product-form__input--pill,
.product--no-media .product-form__input.product-form__input--swatch {
  flex-wrap: wrap;
  margin: 0 auto 1.2rem auto;
}

.product--no-media .product-form__input--dropdown {
  flex-direction: column;
  max-width: 100%;
}

:is(.product-form__input--pill, .product-form__input--swatch) .form__label {
  margin-bottom: 0.2rem;
}

.product-form__input input[type='radio'] {
  clip: rect(0, 0, 0, 0);
  overflow: hidden;
  position: absolute;
  height: 1px;
  width: 1px;
}

.product-form__input input[type='radio']:not(.disabled):not(.visually-disabled) + label > .label-unavailable {
  display: none;
}

.product-form__input--dropdown {
  --swatch-input--size: 2rem;
  margin-bottom: 1.6rem;
}

.product-form__input--dropdown .dropdown-swatch + select {
  padding-left: calc(2.4rem + var(--swatch-input--size));
}

.product-form__input--dropdown .dropdown-swatch {
  position: absolute;
  left: 1.6rem;
  top: calc(50% - var(--swatch-input--size) / 2);
  width: var(--swatch-input--size);
  height: var(--swatch-input--size);
  z-index: 1;
}

/* Custom styles for Pill display type */
.product-form__input--pill input[type='radio'] + label {
  border: var(--variant-pills-border-width) solid rgba(var(--color-foreground), var(--variant-pills-border-opacity));
  background-color: rgb(var(--color-background));
  color: rgba(var(--color-foreground));
  border-radius: var(--variant-pills-radius);
  color: rgb(var(--color-foreground));
  display: inline-block;
  margin: 0.7rem 0.5rem 0.2rem 0;
  padding: 1rem 2rem;
  font-size: 1.4rem;
  letter-spacing: 0.1rem;
  line-height: 1;
  text-align: center;
  transition: border var(--duration-short) ease;
  cursor: pointer;
  position: relative;
}

.product-form__input--pill input[type='radio'] + label:before {
  content: '';
  position: absolute;
  top: calc(var(--variant-pills-border-width) * -1);
  right: calc(var(--variant-pills-border-width) * -1);
  bottom: calc(var(--variant-pills-border-width) * -1);
  left: calc(var(--variant-pills-border-width) * -1);
  z-index: -1;
  border-radius: var(--variant-pills-radius);
  box-shadow: var(--variant-pills-shadow-horizontal-offset) var(--variant-pills-shadow-vertical-offset)
    var(--variant-pills-shadow-blur-radius) rgba(var(--color-shadow), var(--variant-pills-shadow-opacity));
}

.product-form__input--pill input[type='radio'] + label:hover {
  border-color: rgb(var(--color-foreground));
}

.product-form__input--pill input[type='radio']:checked + label {
  background-color: rgb(var(--color-foreground));
  color: rgb(var(--color-background));
}

@media screen and (forced-colors: active) {
  .product-form__input--pill input[type='radio']:checked + label {
    text-decoration: underline;
  }

  .product-form__input--pill input[type='radio']:focus-visible + label {
    outline: transparent solid 1px;
    outline-offset: 2px;
  }
}

.product-form__input--pill input[type='radio']:checked + label::selection {
  background-color: rgba(var(--color-background), 0.3);
}

.product-form__input--pill input[type='radio']:disabled + label,
.product-form__input--pill input[type='radio'].disabled + label {
  border-color: rgba(var(--color-foreground), 0.1);
  color: rgba(var(--color-foreground), 0.6);
  text-decoration: line-through;
}

.product-form__input--pill input[type='radio'].disabled:checked + label,
.product-form__input--pill input[type='radio']:disabled:checked + label {
  color: rgba(var(--color-background), 0.6);
}
.product-form__input--pill input[type='radio']:focus-visible + label {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.5rem rgba(var(--color-foreground), 0.55);
}

/* Fallback */
.product-form__input--pill input[type='radio'].focused + label {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.5rem rgba(var(--color-foreground), 0.55);
}

/* Custom styles for Swatch display type */
.product-form__input--swatch {
  display: flex;
  flex-wrap: wrap;
}

.product-form__input--swatch .swatch-input__input + .swatch-input__label {
  --swatch-input--size: 3.6rem;

  margin: 0.7rem 1.2rem 0.2rem 0;
}

@media screen and (min-width: 750px) {
  .product-form__input--swatch .swatch-input__input + .swatch-input__label {
    --swatch-input--size: 2.8rem;
  }
}
/* End custom styles for Swatch display type */
