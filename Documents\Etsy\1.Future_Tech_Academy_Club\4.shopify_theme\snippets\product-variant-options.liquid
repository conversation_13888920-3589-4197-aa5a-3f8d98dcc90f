{% comment %}
  Renders product variant options

  Accepts:
  - product: {Object} product object.
  - option: {Object} current product_option object.
  - block: {Object} block object.
  - picker_type: {String} type of picker to dispay


  Usage:
  {% render 'product-variant-options',
    product: product,
    option: option,
    block: block
    picker_type: picker_type
  %}
{% endcomment %}
{%- liquid
  assign product_form_id = 'product-form-' | append: section.id
-%}

{%- for value in option.values -%}
  {%- liquid
    assign swatch_focal_point = null
    if value.swatch.image
      assign image_url = value.swatch.image | image_url: width: 50
      assign swatch_value = 'url(' | append: image_url | append: ')'
      assign swatch_focal_point = value.swatch.image.presentation.focal_point
    elsif value.swatch.color
      assign swatch_value = 'rgb(' | append: value.swatch.color.rgb | append: ')'
    else
      assign swatch_value = null
    endif

    assign option_disabled = true
    if value.available
      assign option_disabled = false
    endif
  -%}

  {%- capture input_id -%}
    {{ section.id }}-{{ option.position }}-{{ forloop.index0 -}}
  {%- endcapture -%}

  {%- capture input_name -%}
    {{ option.name }}-{{ option.position }}
  {%- endcapture -%}

  {%- capture input_dataset -%}
    data-product-url="{{ value.product_url }}"
    data-option-value-id="{{ value.id }}"
  {%- endcapture -%}

  {%- capture label_unavailable -%}
    <span class="visually-hidden label-unavailable">
      {{- 'products.product.variant_sold_out_or_unavailable' | t -}}
    </span>
  {%- endcapture -%}

  {%- if picker_type == 'swatch' -%}
    {%- capture help_text -%}
      <span class="visually-hidden">{{ value | escape }}</span>
      {{ label_unavailable }}
    {%- endcapture -%}
    {%
      render 'swatch-input',
      id: input_id,
      name: input_name,
      value: value | escape,
      swatch: value.swatch,
      product_form_id: product_form_id,
      checked: value.selected,
      visually_disabled: option_disabled,
      shape: block.settings.swatch_shape,
      help_text: help_text,
      additional_props: input_dataset
    %}
  {%- elsif picker_type == 'button' -%}
    <input
      type="radio"
      id="{{ input_id }}"
      name="{{ input_name | escape }}"
      value="{{ value | escape }}"
      form="{{ product_form_id }}"
      {% if value.selected %}
        checked
      {% endif %}
      {% if option_disabled %}
        class="disabled"
      {% endif %}
      {{ input_dataset }}
    >
    <label for="{{ input_id }}">
      {{ value -}}
      {{ label_unavailable }}
    </label>
  {%- elsif picker_type == 'dropdown' or picker_type == 'swatch_dropdown' -%}
    <option
      id="{{ input_id }}"
      value="{{ value | escape }}"
      {% if value.selected %}
        selected="selected"
      {% endif %}
      {% if swatch_value and picker_type == 'swatch_dropdown' %}
        data-option-swatch-value="{{ swatch_value }}"
        {% if swatch_focal_point %}
          data-option-swatch-focal-point="{{ swatch_focal_point }}"
        {% endif %}
      {% endif %}
      {{ input_dataset }}
    >
      {% if option_disabled -%}
        {{- 'products.product.value_unavailable' | t: option_value: value -}}
      {%- else -%}
        {{- value -}}
      {%- endif %}
    </option>
  {%- endif -%}
{%- endfor -%}
