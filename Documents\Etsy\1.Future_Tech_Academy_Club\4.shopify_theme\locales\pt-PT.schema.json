{"settings_schema": {"colors": {"name": "Cores", "settings": {"background": {"label": "Fundo"}, "background_gradient": {"label": "Gradiente de fundo", "info": "O gradiente de fundo substitui o fundo sempre que possível."}, "text": {"label": "Texto"}, "button_background": {"label": "Fundo do botão sólido"}, "button_label": {"label": "Etiqueta do botão sólido"}, "secondary_button_label": {"label": "Botão de contorno"}, "shadow": {"label": "Sombra"}}}, "typography": {"name": "Tipografia", "settings": {"type_header_font": {"label": "<PERSON><PERSON><PERSON> de letra"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "Corpo"}, "type_body_font": {"label": "<PERSON><PERSON><PERSON> de letra"}, "heading_scale": {"label": "Expansão"}, "body_scale": {"label": "Expansão"}}}, "social-media": {"name": "Redes sociais", "settings": {"social_twitter_link": {"label": "X / Twitter", "info": "https://x.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://twitter.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Contas de redes sociais"}}}, "currency_format": {"name": "Formato de moeda", "settings": {"currency_code_enabled": {"label": "Códigos de moeda"}, "paragraph": "Os preços de finalização da compra e carrinho mostram sempre os códigos de moeda"}}, "layout": {"name": "Esquema", "settings": {"page_width": {"label": "<PERSON><PERSON><PERSON> da página"}, "spacing_sections": {"label": "Espaço entre as secções do modelo"}, "header__grid": {"content": "Grelha"}, "paragraph__grid": {"content": "Afeta áreas com várias colunas ou linhas"}, "spacing_grid_horizontal": {"label": "Espaço horizontal"}, "spacing_grid_vertical": {"label": "Espaço vertical"}}}, "search_input": {"name": "Comportamento de pesquisa", "settings": {"predictive_search_enabled": {"label": "Sugestões de pesquisa"}, "predictive_search_show_vendor": {"label": "Fornecedor do produto", "info": "<PERSON><PERSON><PERSON><PERSON> quando as sugestões de pesquisa estão ativas"}, "predictive_search_show_price": {"label": "Preço do produto", "info": "<PERSON><PERSON><PERSON><PERSON> quando as sugestões de pesquisa estão ativas"}}}, "global": {"settings": {"header__border": {"content": "Limite"}, "header__shadow": {"content": "Sombra"}, "blur": {"label": "Desfocado"}, "corner_radius": {"label": "Raio do canto"}, "horizontal_offset": {"label": "Compensação horizontal"}, "vertical_offset": {"label": "Compensação vertical"}, "thickness": {"label": "E<PERSON><PERSON><PERSON>"}, "opacity": {"label": "Opacidade"}, "image_padding": {"label": "Preenchi<PERSON> da imagem"}, "text_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do texto"}}}, "badges": {"name": "<PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Canto inferior esquerdo"}, "options__2": {"label": "Canto inferior direito"}, "options__3": {"label": "Canto superior esquerdo"}, "options__4": {"label": "Canto superior direito"}, "label": "Posição nos cartões"}, "sale_badge_color_scheme": {"label": "Esquema de cor do selo de venda"}, "sold_out_badge_color_scheme": {"label": "Esquema de cor do selo de esgotado"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "variant_pills": {"name": "Variantes com forma de comprimidos", "paragraph": "As variantes com forma de comprimidos são uma forma de apresentar as [variantes de produto](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block)"}, "inputs": {"name": "Entradas"}, "content_containers": {"name": "Contentores de conteúdo"}, "popups": {"name": "Menus pendentes e pop-ups", "paragraph": "Afeta áreas como menus pendentes de navegação, pop-ups de modais e pop-ups de carrinho"}, "media": {"name": "Conteúdo multimédia"}, "drawers": {"name": "Gavetas"}, "cart": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "Tipo", "drawer": {"label": "Gaveta"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "notification": {"label": "Notificação pop-up"}}, "show_vendor": {"label": "Fornecedor"}, "show_cart_note": {"label": "Nota do car<PERSON>ho"}, "cart_drawer": {"header": "Painel deslizante do carrinho", "collection": {"label": "Coleção", "info": "Visível quando o painel deslizante do carrinho está vazio"}}}}, "cards": {"name": "Cartões de produtos", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "collection_cards": {"name": "Cartões de coleção", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "blog_cards": {"name": "Cartões de blogue", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "logo": {"name": "Logótipo", "settings": {"logo_image": {"label": "Logótipo"}, "logo_width": {"label": "<PERSON><PERSON><PERSON>"}, "favicon": {"label": "Favicon", "info": "Apresentado a 32 x 32 px"}}}, "brand_information": {"name": "Informação de marca", "settings": {"brand_headline": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "brand_description": {"label": "Descrição"}, "brand_image": {"label": "Imagem"}, "brand_image_width": {"label": "<PERSON><PERSON><PERSON> da <PERSON>"}, "paragraph": {"content": "Apresentado no bloco de informações da marca no rodapé"}}}, "animations": {"name": "Animações", "settings": {"animations_reveal_on_scroll": {"label": "Revelar secções ao percorrer a página"}, "animations_hover_elements": {"options__1": {"label": "Nenhum(a)"}, "options__2": {"label": "Elevação vertical"}, "label": "Efeito ao passar o rato", "info": "Afeta cartões e botões", "options__3": {"label": "Elevação 3D"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Espaçamento", "padding_top": "Superior", "padding_bottom": "Inferior"}, "spacing": "Espaçamento", "colors": {"label": "Esquema de cores", "has_cards_info": "Para alterar o esquema de cores do cartão, atualize as suas definições de tema."}, "heading_size": {"label": "Tamanho do título", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Extra grande"}, "options__5": {"label": "Extremamente grande"}}, "image_shape": {"options__1": {"label": "Predefinição"}, "options__2": {"label": "Arco"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Divisa para a esquerda"}, "options__5": {"label": "Divisa para a direita"}, "options__6": {"label": "Diamante"}, "options__7": {"label": "Paralelogramo"}, "options__8": {"label": "Redonda"}, "label": "Forma de imagem"}, "animation": {"content": "Animações", "image_behavior": {"options__1": {"label": "Nenhum(a)"}, "options__2": {"label": "Movimento de ambiente"}, "label": "Animação", "options__3": {"label": "Posição de fundo fixa"}, "options__4": {"label": "Ampliar ao rodar a roda do rato"}}}}, "announcement-bar": {"name": "Barra de comunicado", "blocks": {"announcement": {"settings": {"text": {"label": "Texto", "default": "Bem-vindo à nossa loja"}, "text_alignment": {"label": "Alinhamento do texto", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "link": {"label": "Ligação"}}, "name": "Comunicado"}}, "settings": {"auto_rotate": {"label": "Rotação automática de comunicados"}, "change_slides_speed": {"label": "Mudar a cada"}, "show_social": {"label": "Ícones de redes sociais", "info": "[G<PERSON><PERSON> contas de redes sociais](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Se<PERSON>or de país/região", "info": "[Gerir países/regiões](/admin/settings/markets)"}, "enable_language_selector": {"label": "Se<PERSON>or de idioma", "info": "[Gerir idiomas](/admin/settings/languages)"}, "heading_utilities": {"content": "Utilitários"}, "paragraph": {"content": "Aparecer apenas em ecrãs grandes"}}, "presets": {"name": "Barra de comunicado"}}, "collage": {"name": "Colagem", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Colagem multimédia"}, "desktop_layout": {"label": "Esquema", "options__1": {"label": "Bloco grande primeiro"}, "options__2": {"label": "Bloco grande por último"}}, "mobile_layout": {"label": "Esquema móvel", "options__1": {"label": "Colagem"}, "options__2": {"label": "Coluna"}}, "card_styles": {"label": "Estilo do cartão", "info": "<PERSON><PERSON>r estilos de cartão individuais em [definições do tema](/editor?context=theme&category=product%20cards)", "options__1": {"label": "Utilizar estilos de cartão individuais"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> todos os cartões de produto"}}, "header_layout": {"content": "Esquema"}}, "blocks": {"image": {"settings": {"image": {"label": "Imagem"}}, "name": "Imagem"}, "product": {"settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Mostrar fundo secundário"}, "second_image": {"label": "Mostrar a segunda imagem ao passar o rato"}}, "name": "Produ<PERSON>"}, "collection": {"settings": {"collection": {"label": "Coleção"}}, "name": "Coleção"}, "video": {"settings": {"cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "O video será reproduzido numa janela pop-up se a secção contiver outros blocos.", "placeholder": "Usar um URL de YouTube ou Vimeo"}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreve o vídeo para que seja acessível a clientes que usam leitores de ecrã. [<PERSON>ber mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "default": "Descrever o vídeo"}}, "name": "Vídeo"}}, "presets": {"name": "Colagem"}}, "collection-list": {"name": "Lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coleções"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "<PERSON><PERSON><PERSON> \"Ver tudo\"", "info": "Visível se a lista tiver mais coleções do que as mostradas"}, "columns_desktop": {"label": "Colunas"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Colunas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_layout": {"content": "Esquema"}}, "blocks": {"featured_collection": {"settings": {"collection": {"label": "Coleção"}}, "name": "Coleção"}}, "presets": {"name": "Lista de coleções"}}, "contact-form": {"name": "Formulário de contacto", "presets": {"name": "Formulário de contacto"}, "settings": {"title": {"default": "Formulário de contacto", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "custom-liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Código Liquid", "info": "Adicione fragmentos de aplicação ou outro código para criar personalizações avançadas. [Saber mais](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personalizado"}}, "featured-blog": {"name": "Publicações no blogue", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Publicações no blogue"}, "blog": {"label": "Blogue"}, "post_limit": {"label": "Número de publicações"}, "show_view_all": {"label": "<PERSON><PERSON><PERSON> \"Ver tudo\"", "info": "Visível se o blogue tiver mais publicações do que as mostradas"}, "show_image": {"label": "Imagem em destaque"}, "show_date": {"label": "Data"}, "show_author": {"label": "Autor"}, "columns_desktop": {"label": "Colunas"}, "layout_header": {"content": "Esquema"}, "text_header": {"content": "Texto"}}, "presets": {"name": "Publicações no blogue"}}, "featured-collection": {"name": "Coleção em destaque", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coleção em destaque"}, "collection": {"label": "Coleção"}, "products_to_show": {"label": "Número de produtos"}, "show_view_all": {"label": "<PERSON><PERSON><PERSON> \"Ver tudo\"", "info": "Visível se a coleção tiver mais produtos do que os mostrados"}, "header": {"content": "Cartão de produtos"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "show_secondary_image": {"label": "Mostrar a segunda imagem ao passar o rato"}, "show_vendor": {"label": "Fornecedor"}, "show_rating": {"label": "Classificação de produto", "info": "É necessária uma app para as classificações. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "<PERSON><PERSON><PERSON> rápida"}, "columns_desktop": {"label": "Colunas"}, "description": {"label": "Descrição"}, "show_description": {"label": "Mostrar descrição da coleção do painel de admin"}, "description_style": {"label": "Estilo da descrição", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}, "view_all_style": {"options__1": {"label": "Ligação"}, "options__2": {"label": "Botão de contorno"}, "options__3": {"label": "Botão sólido"}, "label": "<PERSON><PERSON><PERSON> \"ver tudo\""}, "enable_desktop_slider": {"label": "<PERSON><PERSON><PERSON>"}, "full_width": {"label": "Produtos com largura total"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Colunas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "header_text": {"content": "Texto"}, "header_collection": {"content": "Esquema da coleção"}}, "presets": {"name": "Coleção em destaque"}}, "footer": {"name": "Rodapé", "blocks": {"link_list": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "Ligações rápidas"}, "menu": {"label": "<PERSON><PERSON>"}}, "name": "<PERSON><PERSON>"}, "text": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "subtext": {"label": "Subtexto", "default": "<p>Partilhe informações de contacto, detalhes da loja e conteúdo de marca com os seus clientes.</p>"}}, "name": "Texto"}, "brand_information": {"name": "Informação de marca", "settings": {"paragraph": {"content": "<PERSON><PERSON>r informações da marca em [definições de tema](/editor?context=theme&category=brand%20information)"}, "show_social": {"label": "Ícones de redes sociais", "info": "[G<PERSON><PERSON> contas de redes sociais](/editor?context=theme&category=social%20media)"}}}}, "settings": {"newsletter_enable": {"label": "Registo de e-mail"}, "newsletter_heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "Subscreva os nossos e-mails"}, "header__1": {"info": "Adição de registos [perfis de clientes](https://help.shopify.com/manual/customers/manage-customers)", "content": "Registo de e-mail"}, "show_social": {"label": "Ícones de redes sociais", "info": "[G<PERSON><PERSON> contas de redes sociais](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Se<PERSON>or de país/região", "info": "[Gerir países/regiões](/admin/settings/markets)"}, "enable_language_selector": {"label": "Se<PERSON>or de idioma", "info": "[Gerir idiomas](/admin/settings/languages)"}, "payment_enable": {"label": "Ícones do método de pagamento"}, "margin_top": {"label": "Margem superior"}, "show_policy": {"label": "Ligações das políticas", "info": "[<PERSON><PERSON><PERSON>](/admin/settings/legal)"}, "header__9": {"content": "Utilitários"}, "enable_follow_on_shop": {"label": "Seguir na Shop", "info": "O Shop Pay tem de ser ativado. [<PERSON>ber mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}}}, "header": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"logo_position": {"label": "Posição do logótipo", "options__1": {"label": "Intermédio à esquerda"}, "options__2": {"label": "Canto superior esquerdo"}, "options__3": {"label": "Superior centro"}, "options__4": {"label": "Intermédio ao centro"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Linha do separador"}, "margin_bottom": {"label": "Margem inferior"}, "menu_type_desktop": {"label": "Tipo de menu", "options__1": {"label": "<PERSON><PERSON> pendente"}, "options__2": {"label": "Mega menu"}, "options__3": {"label": "<PERSON><PERSON>"}}, "mobile_logo_position": {"label": "Posição móvel do logótipo", "options__1": {"label": "Centro"}, "options__2": {"label": "E<PERSON>rda"}}, "logo_help": {"content": "Edite o seu logótipo em [definições de tema](/editor?context=theme&category=logo)"}, "sticky_header_type": {"label": "Cabeçalho fixo", "options__1": {"label": "Nenhum(a)"}, "options__2": {"label": "Ao rodar a roda do rato"}, "options__3": {"label": "Sempre"}, "options__4": {"label": "Se<PERSON><PERSON>, reduzir o tamanho do logótipo"}}, "enable_country_selector": {"label": "Se<PERSON>or de país/região", "info": "[Gerir países/regiões](/admin/settings/markets)"}, "enable_language_selector": {"label": "Se<PERSON>or de idioma", "info": "[Gerir idiomas](/admin/settings/languages)"}, "header__1": {"content": "Cor"}, "menu_color_scheme": {"label": "Esquema de cores do menu"}, "enable_customer_avatar": {"label": "Avatar da conta de cliente", "info": "Apenas visível quando os clientes estão registados no Shop. [<PERSON><PERSON>r contas de cliente](/admin/settings/customer_accounts)"}, "header__utilities": {"content": "Utilitários"}}}, "image-banner": {"name": "Faixa de imagem", "settings": {"image": {"label": "Imagem 1"}, "image_2": {"label": "Imagem 2"}, "stack_images_on_mobile": {"label": "Empilhar imagens"}, "show_text_box": {"label": "Recipiente"}, "image_overlay_opacity": {"label": "Opacidade de sobreposição"}, "show_text_below": {"label": "Recipiente"}, "image_height": {"label": "Altura", "options__1": {"label": "Adaptar à primeira imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Superior centro"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Intermédio à esquerda"}, "options__5": {"label": "Intermédio ao centro"}, "options__6": {"label": "Intermédio à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Inferior centro"}, "options__9": {"label": "Canto inferior direito"}, "label": "Posição"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "mobile": {"content": "Esquema para dispositivo móvel"}, "content": {"content": "<PERSON><PERSON><PERSON><PERSON>"}}, "blocks": {"heading": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Banner de imagem"}}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"settings": {"text": {"label": "Texto", "default": "Dê informações aos clientes sobre a(s) imagem(s) ou o conteúdo do banner no modelo."}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON>"}}, "name": "Texto"}, "buttons": {"settings": {"button_label_1": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta do botão"}, "button_link_1": {"label": "Ligação"}, "button_style_secondary_1": {"label": "Estilo do contorno"}, "button_label_2": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta do botão"}, "button_link_2": {"label": "Ligação"}, "button_style_secondary_2": {"label": "Estilo do contorno"}, "header_1": {"content": "Botão 1"}, "header_2": {"content": "Botão 2"}}, "name": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "Faixa de imagem"}}, "image-with-text": {"name": "Imagem com texto", "settings": {"image": {"label": "Imagem"}, "height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "label": "Altura", "options__4": {"label": "Grande"}}, "layout": {"options__1": {"label": "<PERSON>m primeiro"}, "options__2": {"label": "Segunda imagem"}, "label": "Posicionamento"}, "desktop_image_width": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON>"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento", "options__2": {"label": "Ao centro"}}, "desktop_content_position": {"options__1": {"label": "Em cima"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON> baixo"}, "label": "Posição"}, "content_layout": {"options__1": {"label": "Sem sobreposição"}, "options__2": {"label": "Sobreposição"}, "label": "Esquema"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento em dispositivos móveis", "options__2": {"label": "Ao centro"}}, "header": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header_colors": {"content": "Cores"}}, "blocks": {"heading": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Imagem com texto"}}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"settings": {"text": {"label": "Texto", "default": "<p>Emparelhe texto com uma imagem para destacar o produto, a coleção ou a publicação no blogue escolhido. Adicione informações sobre disponibilidade, estilo ou até mesmo uma avaliação.</p>"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}}}, "name": "Texto"}, "button": {"settings": {"button_label": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta do botão"}, "button_link": {"label": "Ligação"}, "outline_button": {"label": "Estilo do contorno"}}, "name": "Botão"}, "caption": {"name": "<PERSON>a", "settings": {"text": {"label": "Texto", "default": "Adicionar um <PERSON>"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON>a"}, "options__2": {"label": "Mai<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Imagem com texto"}}, "main-article": {"name": "Publicação no blogue", "blocks": {"featured_image": {"settings": {"image_height": {"label": "<PERSON><PERSON> da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}}}, "name": "Imagem em destaque"}, "title": {"settings": {"blog_show_date": {"label": "Data"}, "blog_show_author": {"label": "Autor"}}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "share": {"name": "Partilhar", "settings": {"text": {"label": "Texto", "default": "Partilhar"}}}}}, "main-blog": {"name": "Publicações no blogue", "settings": {"show_image": {"label": "Imagem em destaque"}, "show_date": {"label": "Data"}, "show_author": {"label": "Autor"}, "layout": {"label": "Esquema", "options__1": {"label": "Grelha"}, "options__2": {"label": "Colagem"}}, "image_height": {"label": "<PERSON><PERSON> da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"subtotal": {"name": "Subtotal"}, "buttons": {"name": "Botão de finalização da compra"}}}, "main-cart-items": {"name": "<PERSON><PERSON>"}, "main-collection-banner": {"name": "Faixa de coleção", "settings": {"paragraph": {"content": "O<PERSON> de<PERSON>hes da coleção são [geridos no seu painel de admin](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Descrição"}, "show_collection_image": {"label": "Imagem"}}}, "main-collection-product-grid": {"name": "<PERSON>rel<PERSON> de produtos", "settings": {"products_per_page": {"label": "Produtos por página"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "show_secondary_image": {"label": "Mostrar a segunda imagem ao passar o rato"}, "show_vendor": {"label": "Fornecedor"}, "header__1": {"content": "Filtragem e ordenação"}, "enable_tags": {"label": "<PERSON><PERSON><PERSON>", "info": "Personalize filtros com a [aplicação Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_filtering": {"label": "<PERSON><PERSON><PERSON>", "info": "Personalize filtros com a [aplicação Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Ordenação"}, "header__3": {"content": "Cartão de produtos"}, "show_rating": {"label": "Classificação de produto", "info": "É necessária uma app para as classificações de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"}, "columns_desktop": {"label": "Colunas"}, "columns_mobile": {"label": "Colunas em dispositivos móveis", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "filter_type": {"label": "Filtrar esquema", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Gaveta"}}, "quick_add": {"label": "<PERSON><PERSON><PERSON> rápida", "options": {"option_1": "Nenhum(a)", "option_2": "Padrão", "option_3": "Em massa"}}}}, "main-list-collections": {"name": "Página da lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coleções"}, "sort": {"label": "Ordenar coleções", "options__1": {"label": "Alfabeticamente, A-Z"}, "options__2": {"label": "Alfabeticamente, Z-A"}, "options__3": {"label": "<PERSON>, mais recentes"}, "options__4": {"label": "<PERSON>, mais antigos"}, "options__5": {"label": "Contagem de produtos, alta para baixa"}, "options__6": {"label": "Contagem de produtos, baixa para alta"}}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "columns_desktop": {"label": "Colunas"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Colunas em dispositivos móveis", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "Rodapé de palavra-passe"}, "main-password-header": {"name": "Cabeçalho de palavra-passe", "settings": {"logo_help": {"content": "Edite o seu logótipo em [definições de tema](/editor?context=theme&category=logo)"}}}, "main-product": {"name": "Informações do produto", "blocks": {"text": {"settings": {"text": {"label": "Texto", "default": "Bloco de texto"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}, "name": "Texto"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pendente"}, "options__2": {"label": "Comprimidos"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "Saber mais sobre as [amostras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nas opções de produtos", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Quadrado"}, "options__3": {"label": "Nenhum(a)"}}}}, "buy_buttons": {"settings": {"show_dynamic_checkout": {"label": "Botões dinâmicos de finalização da compra", "info": "Os clientes vão ver a sua opção de pagamento preferida. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": " Opções de envio do cartão de oferta", "info": "Os clientes podem adicionar uma mensagem pessoal e agendar a data de envio. [Saber mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}, "name": "Botão de compra"}, "share": {"settings": {"text": {"label": "Texto", "default": "Partilhar"}}, "name": "Partilhar"}, "collapsible_tab": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON> re<PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> da linha da página"}, "icon": {"label": "Ícone", "options__1": {"label": "Nenhum(a)"}, "options__2": {"label": "Maçã"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Caixa"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON> de conversa"}, "options__8": {"label": "Marca de verificação"}, "options__9": {"label": "Pranchet<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON> l<PERSON>"}, "options__11": {"label": "Sem produtos lácteos"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fogo"}, "options__15": {"label": "<PERSON><PERSON>"}, "options__16": {"label": "Coração"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Fol<PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Relâmpago"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "Cadeado"}, "options__23": {"label": "Marcador de mapa"}, "options__24": {"label": "Sem frutos de casca rija"}, "options__25": {"label": "Calças"}, "options__26": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__27": {"label": "Pimenta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avião"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de preço"}, "options__32": {"label": "Ponto de interrogação"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Devolução"}, "options__35": {"label": "Régua"}, "options__36": {"label": "Prato"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Sapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Floco de neve"}, "options__41": {"label": "Estrela"}, "options__42": {"label": "Cronómetro"}, "options__43": {"label": "Camião"}, "options__44": {"label": "<PERSON><PERSON>"}}}, "name": "<PERSON><PERSON> re<PERSON>"}, "popup": {"settings": {"link_label": {"label": "Etiqueta de ligação", "default": "Texto da ligação pop-up"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "name": "Pop-up"}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "pickup_availability": {"name": "Disponibilidade de recolha"}, "description": {"name": "Descrição"}, "rating": {"name": "Classificação do produto", "settings": {"paragraph": {"content": "É necessária uma app para as classificações de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"}}}, "complementary_products": {"name": "<PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "<PERSON><PERSON><PERSON> produtos complementares na [aplicação Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Emparelha com"}, "make_collapsible_row": {"label": "<PERSON><PERSON> re<PERSON>"}, "icon": {"info": "Visível quando é selecionada a linha recolhível"}, "product_list_limit": {"label": "Número de produtos"}, "products_per_page": {"label": "Produtos por página"}, "pagination_style": {"label": "Paginação", "options": {"option_1": "Pontos", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Números"}}, "product_card": {"heading": "Cartão de produtos"}, "image_ratio": {"label": "Proporção de imagem", "options": {"option_1": "Retrato", "option_2": "Quadrado"}}, "enable_quick_add": {"label": "<PERSON><PERSON><PERSON> rápida"}}}, "icon_with_text": {"name": "Ícone com texto", "settings": {"layout": {"label": "Esquema", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "heading": {"info": "Deixe em branco para ocultar este emparelhamento"}, "icon_1": {"label": "Ícone"}, "image_1": {"label": "Imagem"}, "heading_1": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "icon_2": {"label": "Ícone"}, "image_2": {"label": "Imagem"}, "heading_2": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "icon_3": {"label": "Ícone"}, "image_3": {"label": "Imagem"}, "heading_3": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pairing_1": {"label": "Emparelhamento 1", "info": "Escolha um ícone ou adicione uma imagem para cada emparelhamento"}, "pairing_2": {"label": "Emparelhamento 2"}, "pairing_3": {"label": "Emparelhamento 3"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}}, "inventory": {"name": "Estado do inventário", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Limite do inventário baixo"}, "show_inventory_quantity": {"label": "Contagem de inventário"}}}}, "settings": {"header": {"content": "Conteúdo multimédia"}, "enable_video_looping": {"label": "Repetir vídeo"}, "enable_sticky_info": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "hide_variants": {"label": "Ocultar outro conteúdo multimédia da variante quando um é selecionado"}, "gallery_layout": {"label": "Esquema", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON> colu<PERSON>"}, "options__3": {"label": "Miniaturas"}, "options__4": {"label": "Carrossel de miniaturas"}}, "media_size": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}, "mobile_thumbnails": {"label": "Esquema móvel", "options__1": {"label": "<PERSON><PERSON> colu<PERSON>"}, "options__2": {"label": "Mostrar miniaturas"}, "options__3": {"label": "Ocultar miniaturas"}}, "media_position": {"label": "Posição", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "Zoom", "options__1": {"label": "Abrir janela modal (lightbox)"}, "options__2": {"label": "Clicar e passar o cursor"}, "options__3": {"label": "Sem zoom"}}, "constrain_to_viewport": {"label": "Ajustar à altura do ecrã"}, "media_fit": {"label": "Ajustar", "options__1": {"label": "Original"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}}, "main-search": {"name": "Resultados da pesquisa", "settings": {"image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "show_secondary_image": {"label": "Mostrar a segunda imagem ao passar o rato"}, "show_vendor": {"label": "Fornecedor"}, "header__1": {"content": "Cartão de produtos"}, "header__2": {"content": "Cartão de blogue"}, "article_show_date": {"label": "Data"}, "article_show_author": {"label": "Autor"}, "show_rating": {"label": "Classificação de produto", "info": "É necessária uma app para as classificações de produto. [<PERSON><PERSON> mais](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"}, "columns_desktop": {"label": "Colunas"}, "columns_mobile": {"label": "Colunas em dispositivos móveis", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multicolumn": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "image_width": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Um terço da largura da coluna"}, "options__2": {"label": "Metade da largura da coluna"}, "options__3": {"label": "Largura total da coluna"}}, "image_ratio": {"label": "Proporção", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "Alinhamento de colunas", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}}, "background_style": {"label": "Fundo secundário", "options__1": {"label": "Nenhum(a)"}, "options__2": {"label": "Mostrar como fundo da coluna"}}, "button_label": {"label": "Etiqueta", "default": "Etiqueta do botão", "info": "Deixe em branco para ocultar"}, "button_link": {"label": "Ligação"}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "columns_desktop": {"label": "Colunas"}, "header_mobile": {"content": "Esquema para dispositivo móvel"}, "columns_mobile": {"label": "Colunas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_text": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header_image": {"content": "Imagem"}, "header_layout": {"content": "Esquema"}, "header_button": {"content": "Botão"}}, "blocks": {"column": {"settings": {"image": {"label": "Imagem"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coluna"}, "text": {"label": "Descrição", "default": "<p>Emparelhe texto com uma imagem para destacar o produto, a coleção ou a publicação no blogue escolhido. Adicione informações sobre disponibilidade, estilo ou até mesmo uma avaliação.</p>"}, "link_label": {"label": "Etiqueta de ligação", "info": "Deixe em branco para ocultar"}, "link": {"label": "Ligação"}}, "name": "Coluna"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "newsletter": {"name": "Registo de e-mail", "settings": {"full_width": {"label": "Largura total"}, "paragraph": {"content": "Adição de registos [perfis de clientes](https://help.shopify.com/manual/customers/manage-customers)"}}, "blocks": {"heading": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Subscreva os nossos e-mails"}}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "paragraph": {"settings": {"paragraph": {"label": "Texto", "default": "<p>Seja a primeira pessoa a saber sobre novas coleções e ofertas exclusivas.</p>"}}, "name": "Texto"}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Registo de e-mail"}}, "page": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "rich-text": {"name": "Texto formatado", "settings": {"full_width": {"label": "Largura total"}, "desktop_content_position": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Posição do conteúdo"}, "content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo"}}, "blocks": {"heading": {"settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Fale sobre a sua marca"}}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"settings": {"text": {"label": "Texto", "default": "<p>Partilhe informações sobre a sua marca com os clientes. Descreva um produto, faça comunicados ou dê as boas-vindas aos clientes na loja.</p>"}}, "name": "Texto"}, "buttons": {"settings": {"button_label_1": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta do botão"}, "button_link_1": {"label": "Ligação"}, "button_style_secondary_1": {"label": "Estilo do contorno"}, "button_label_2": {"label": "Etiqueta", "info": "Deixe a etiqueta em branco para ocultar"}, "button_link_2": {"label": "Ligação"}, "button_style_secondary_2": {"label": "Estilo do contorno"}, "header_button1": {"content": "Botão 1"}, "header_button2": {"content": "Botão 2"}}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "caption": {"name": "<PERSON>a", "settings": {"text": {"label": "Texto", "default": "Adicionar um <PERSON>"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON>a"}, "options__2": {"label": "Mai<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Texto formatado"}}, "apps": {"name": "Aplicações", "settings": {"include_margins": {"label": "<PERSON><PERSON><PERSON> as margen<PERSON> de sec<PERSON> as mesmas que o tema"}}, "presets": {"name": "Aplicações"}}, "video": {"name": "Vídeo", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "Vídeo"}, "cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "Usar um URL do YouTube ou Vimeo"}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreve o vídeo para quem usa leitor de ecrã"}, "image_padding": {"label": "Adicionar preenchimento de imagem", "info": "Selecione um preenchimento de imagem se não quer que a sua imagem de capa seja cortada."}, "full_width": {"label": "Largura total"}, "video": {"label": "Vídeo"}, "enable_video_looping": {"label": "Repetir vídeo"}, "header__1": {"content": "Vídeo hospedado na Shopify"}, "header__2": {"content": "Ou incorporar vídeo a partir de URL"}, "header__3": {"content": "Esquema"}, "paragraph": {"content": "Aperece quando nenhum vídeo hospedado na Shopify está selecionado"}}, "presets": {"name": "Vídeo"}}, "featured-product": {"name": "Produto em destaque", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "Bloco de texto"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pendente"}, "options__2": {"label": "Forma de comprimidos"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "Saber mais sobre as [amostras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nas opções de produtos", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Quadrado"}, "options__3": {"label": "Nenhum(a)"}}}}, "buy_buttons": {"name": "Botão de compra", "settings": {"show_dynamic_checkout": {"label": "Botões dinâmicos de finalização da compra", "info": "Os clientes vão ver a sua opção de pagamento preferida. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Descrição"}, "share": {"name": "Partilhar", "settings": {"featured_image_info": {"content": "Se incluir uma ligação nas publicações das redes sociais, a imagem em destaque da página será demonstrada como a imagem de pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "É incluído um título de loja e descrição com a imagem de pré-visualização. [Saber mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texto", "default": "Partilhar"}}}, "rating": {"name": "Classificação do produto", "settings": {"paragraph": {"content": "É necessária uma app para as classificações de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}}}, "settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Fundo secundário"}, "header": {"content": "Conteúdo multimédia"}, "enable_video_looping": {"label": "Repetir vídeo"}, "hide_variants": {"label": "Ocultar conteúdo multimédia das variantes não selecionadas no desktop"}, "media_position": {"label": "Posição", "info": "A posição é automaticamente otimizada para dispositivos móveis.", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "presets": {"name": "Produto em destaque"}}, "email-signup-banner": {"name": "Faixa de registo de e-mail", "settings": {"paragraph": {"content": "Adição de registos [perfis de clientes](https://help.shopify.com/manual/customers/manage-customers)"}, "image": {"label": "Imagem de fundo"}, "show_background_image": {"label": "Mostrar imagem de fundo"}, "show_text_box": {"label": "Recipiente"}, "image_overlay_opacity": {"label": "Opacidade de sobreposição"}, "show_text_below": {"label": "Empilhar texto abaixo da imagem"}, "image_height": {"label": "Altura", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Superior centro"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Intermédio à esquerda"}, "options__5": {"label": "Intermédio ao centro"}, "options__6": {"label": "Intermédio à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Inferior centro"}, "options__9": {"label": "Canto inferior direito"}, "label": "Posição"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "header": {"content": "Esquema para dispositivo móvel"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "color_scheme": {"info": "Visível quando o recetor é exibido."}, "content_header": {"content": "<PERSON><PERSON><PERSON><PERSON>"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "Abre brevemente"}}}, "paragraph": {"name": "Texto", "settings": {"paragraph": {"label": "Texto", "default": "<p><PERSON>ja a primeira pessoa a saber quando é o lançamento.</p>"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "label": "<PERSON><PERSON><PERSON>"}}}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Faixa de registo de e-mail"}}, "slideshow": {"name": "Apresentação de diapositivos", "settings": {"layout": {"label": "Esquema", "options__1": {"label": "Largura total"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "slide_height": {"label": "Altura", "options__1": {"label": "Adaptar à primeira imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Paginação", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Pontos"}, "options__3": {"label": "Números"}}, "auto_rotate": {"label": "Rodar diapositivos automaticamente"}, "change_slides_speed": {"label": "Mudar diapositivos a cada"}, "mobile": {"content": "Esquema móvel"}, "show_text_below": {"label": "Empilhar texto abaixo da imagem"}, "accessibility": {"content": "Acessibilidade", "label": "Descrição da apresentação de diapositivos", "info": "Descreve a apresentação de diapositivos para quem usa leitor de ecrã", "default": "Apresentação de diapositivos sobre a nossa marca"}}, "blocks": {"slide": {"name": "Diapositivo", "settings": {"image": {"label": "Imagem"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "Diapositivo de imagem"}, "subheading": {"label": "Subtítulo", "default": "Conte a história da sua marca através de imagens"}, "button_label": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta do botão"}, "link": {"label": "Ligação"}, "secondary_style": {"label": "Estilo do contorno"}, "box_align": {"label": "Posição do conteúdo", "options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Superior centro"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Intermédio à esquerda"}, "options__5": {"label": "Intermédio ao centro"}, "options__6": {"label": "Intermédio à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Inferior centro"}, "options__9": {"label": "Canto inferior direito"}}, "show_text_box": {"label": "Recipiente"}, "text_alignment": {"label": "Alinhamento de conteúdo", "option_1": {"label": "E<PERSON>rda"}, "option_2": {"label": "Ao centro"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacidade de sobreposição"}, "text_alignment_mobile": {"label": "Alinhamento do conteúdo em dispositivos móveis", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "header_button": {"content": "Botão"}, "header_layout": {"content": "Esquema"}, "header_text": {"content": "Texto"}, "header_colors": {"content": "Cores"}}}}, "presets": {"name": "Apresentação de diapositivos"}}, "collapsible_content": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"caption": {"label": "<PERSON>a"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "heading_alignment": {"label": "Alinhamento do título", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Ao centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Recipiente", "options__1": {"label": "<PERSON><PERSON><PERSON> contentor"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Contentor de se<PERSON>ção"}}, "container_color_scheme": {"label": "Esquema de cores do contentor"}, "open_first_collapsible_row": {"label": "Abrir primeira linha"}, "header": {"content": "Imagem"}, "image": {"label": "Imagem"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Grande"}}, "desktop_layout": {"label": "Posicionamento", "options__1": {"label": "Primeira imagem"}, "options__2": {"label": "Segunda imagem"}}, "layout_header": {"content": "Esquema"}, "section_color_scheme": {"label": "Esquema de cores da secção"}}, "blocks": {"collapsible_row": {"name": "<PERSON><PERSON> re<PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON> re<PERSON>"}, "row_content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> da linha da página"}, "icon": {"label": "Ícone", "options__1": {"label": "Nenhum(a)"}, "options__2": {"label": "Maçã"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Caixa"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON> de conversa"}, "options__8": {"label": "Marca de verificação"}, "options__9": {"label": "Pranchet<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON> l<PERSON>"}, "options__11": {"label": "Sem produtos lácteos"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fogo"}, "options__15": {"label": "<PERSON><PERSON>"}, "options__16": {"label": "Coração"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Fol<PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Relâmpago"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "Cadeado"}, "options__23": {"label": "Marcador de mapa"}, "options__24": {"label": "Sem frutos de casca rija"}, "options__25": {"label": "Calças"}, "options__26": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__27": {"label": "Pimenta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avião"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de preço"}, "options__32": {"label": "Ponto de interrogação"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Devolução"}, "options__35": {"label": "Régua"}, "options__36": {"label": "Prato"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Sapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Floco de neve"}, "options__41": {"label": "Estrela"}, "options__42": {"label": "Cronómetro"}, "options__43": {"label": "Camião"}, "options__44": {"label": "<PERSON><PERSON>"}}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "main-account": {"name": "Conta"}, "main-activate-account": {"name": "Ativação de conta"}, "main-addresses": {"name": "Endereços"}, "main-login": {"name": "<PERSON><PERSON><PERSON>", "shop_login_button": {"enable": "Ativar a opção Iniciar sessão com o Shop"}}, "main-order": {"name": "Encomenda"}, "main-register": {"name": "Registo"}, "main-reset-password": {"name": "Redefinição de palavra-passe"}, "related-products": {"name": "Produtos relacionados", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Número de produtos"}, "columns_desktop": {"label": "Colunas"}, "paragraph__1": {"content": "Os produtos relacionados podem ser geridos na [aplicação Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)", "default": "<PERSON><PERSON><PERSON><PERSON> poderá gostar de"}, "header__2": {"content": "Cartão de produtos"}, "image_ratio": {"label": "Proporção de imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrado"}}, "show_secondary_image": {"label": "Mostrar a segunda imagem ao passar o rato"}, "show_vendor": {"label": "Fornecedor"}, "show_rating": {"label": "Classificação de produto", "info": "É necessária uma app para as classificações de produto. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"}, "columns_mobile": {"label": "Colunas em dispositivos móveis", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multirow": {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "settings": {"image": {"label": "Imagem"}, "image_height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}, "label": "Altura"}, "desktop_image_width": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON>"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON>a"}, "label": "Estilo de texto"}, "button_style": {"options__1": {"label": "Botão sólido"}, "options__2": {"label": "Botão de contorno"}, "label": "Estilo do botão"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "desktop_content_position": {"options__1": {"label": "Em cima"}, "options__2": {"label": "Ao meio"}, "options__3": {"label": "<PERSON> baixo"}, "label": "Posição"}, "image_layout": {"options__1": {"label": "Alternar da esquerda"}, "options__2": {"label": "Alternar da direita"}, "options__3": {"label": "Alinhado à esquerda"}, "options__4": {"label": "Alinhado à direita"}, "label": "Posicionamento"}, "container_color_scheme": {"label": "Esquema de cores do contentor"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento em dispositivos móveis"}, "header": {"content": "Imagem"}, "header_2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header_3": {"content": "Cores"}}, "blocks": {"row": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Imagem"}, "caption": {"label": "<PERSON>a", "default": "<PERSON>a"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON>"}, "text": {"label": "Texto", "default": "<p>Emparelhe texto com uma imagem para destacar o produto, a coleção ou a publicação no blogue escolhido. Adicione informações sobre disponibilidade, estilo ou até mesmo uma avaliação.</p>"}, "button_label": {"label": "Etiqueta do botão", "default": "Etiqueta do botão", "info": "Deixe em branco para ocultar"}, "button_link": {"label": "Ligação do botão"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}}, "quick-order-list": {"name": "Lista de encomendas rápida", "settings": {"show_image": {"label": "Imagens"}, "show_sku": {"label": "SKU"}, "variants_per_page": {"label": "<PERSON><PERSON><PERSON> por página"}}, "presets": {"name": "Lista de encomendas rápida"}}}}