{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-search.css' | asset_url | stylesheet_tag }}

{%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
  {{ 'component-facets.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{% if section.settings.image_shape == 'blob' %}
  {{ 'mask-blobs.css' | asset_url | stylesheet_tag }}
{%- endif -%}

<script src="{{ 'main-search.js' | asset_url }}" defer="defer"></script>

<style>
  .template-search__header {
    margin-bottom: 3rem;
  }

  .template-search__search {
    margin: 0 auto 3.5rem;
    max-width: 74.1rem;
  }

  .template-search__search .search {
    margin-top: 3rem;
  }

  .template-search--empty {
    padding-bottom: 18rem;
  }

  @media screen and (min-width: 750px) {
    .template-search__header {
      margin-bottom: 5rem;
    }
  }

  .search__button .icon {
    height: 1.8rem;
  }
</style>

{%- liquid
  assign sort_by = search.sort_by | default: search.default_sort_by
  assign terms = search.terms | escape
  assign search_url = '?q=' | append: terms | append: '&options%5Bprefix%5D=last&sort_by=' | append: sort_by
-%}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{% paginate search.results by 24 %}
  <div class="template-search{% unless search.performed and search.results_count > 0 %} template-search--empty{% endunless %} section-{{ section.id }}-padding">
    <div class="template-search__header page-width{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
      <h1 class="h2 center">
        {%- if search.performed -%}
          {{- 'templates.search.title' | t -}}
        {%- else -%}
          {{- 'general.search.search' | t -}}
        {%- endif -%}
      </h1>
      <div class="template-search__search">
        {%- if settings.predictive_search_enabled -%}
          <predictive-search data-loading-text="{{ 'accessibility.loading' | t }}">
        {%- endif -%}
        <main-search>
          <form action="{{ routes.search_url }}" method="get" role="search" class="search">
            <div class="field">
              <input
                class="search__input field__input"
                id="Search-In-Template"
                type="search"
                name="q"
                value="{{ search.terms | escape }}"
                placeholder="{{ 'general.search.search' | t }}"
                {%- if settings.predictive_search_enabled -%}
                  role="combobox"
                  aria-expanded="false"
                  aria-owns="predictive-search-results"
                  aria-controls="predictive-search-results"
                  aria-haspopup="listbox"
                  aria-autocomplete="list"
                  autocorrect="off"
                  autocomplete="off"
                  autocapitalize="off"
                  spellcheck="false"
                {%- endif -%}
              >
              <label class="field__label" for="Search-In-Template">{{ 'general.search.search' | t }}</label>
              <input name="options[prefix]" type="hidden" value="last">

              {%- if settings.predictive_search_enabled -%}
                <div class="predictive-search predictive-search--search-template" tabindex="-1" data-predictive-search>
                  {%- render 'loading-spinner', class: 'predictive-search__loading-state' -%}
                </div>

                <span class="predictive-search-status visually-hidden" role="status" aria-hidden="true"></span>
              {%- endif -%}

              <button
                type="reset"
                class="reset__button field__button{% if search.terms == blank %} hidden{% endif %}"
                aria-label="{{ 'general.search.reset' | t }}"
              >
                <span class="svg-wrapper">
                  {{- 'icon-reset.svg' | inline_asset_content -}}
                </span>
              </button>
              <button type="submit" class="search__button field__button" aria-label="{{ 'general.search.search' | t }}">
                <span class="svg-wrapper">
                  {{- 'icon-search.svg' | inline_asset_content -}}
                </span>
              </button>
            </div>
          </form>
        </main-search>
        {%- if settings.predictive_search_enabled -%}
          </predictive-search>
        {%- endif -%}
      </div>
      {%- if search.performed -%}
        {%- unless section.settings.enable_filtering or section.settings.enable_sorting -%}
          {%- if search.results_count > 0 -%}
            <p role="status">
              {{ 'templates.search.results_with_count_and_term' | t: terms: search.terms, count: search.results_count }}
            </p>
          {%- endif -%}
        {%- endunless -%}
        {%- if search.results_count == 0 and search.filters == empty -%}
          <p role="status">{{ 'templates.search.no_results' | t: terms: search.terms }}</p>
        {%- endif -%}
      {%- endif -%}
    </div>
    {%- if search.performed -%}
      {%- if section.settings.enable_sorting
        and section.settings.filter_type == 'vertical'
        and search.filters != empty
      -%}
        <facet-filters-form class="facets facets-vertical-sort page-width small-hide">
          <form class="facets-vertical-form" id="FacetSortForm">
            <div class="facet-filters sorting caption">
              <div class="facet-filters__field">
                <h2 class="facet-filters__label caption-large text-body">
                  <label for="SortBy">{{ 'products.facets.sort_by_label' | t }}</label>
                </h2>
                <div class="select">
                  {%- assign sort_by = search.sort_by | default: search.default_sort_by -%}
                  <select
                    name="sort_by"
                    class="facet-filters__sort select__select caption-large"
                    id="SortBy"
                    aria-describedby="a11y-refresh-page-message"
                  >
                    {%- for option in search.sort_options -%}
                      <option
                        value="{{ option.value | escape }}"
                        {% if option.value == sort_by %}
                          selected="selected"
                        {% endif %}
                      >
                        {{ option.name | escape }}
                      </option>
                    {%- endfor -%}
                  </select>
                  <span class="svg-wrapper">
                    {{- 'icon-caret.svg' | inline_asset_content -}}
                  </span>
                </div>
              </div>
            </div>

            <div class="product-count-vertical light" role="status">
              <h2 class="product-count__text text-body">
                <span id="ProductCountDesktop">
                  {%- if search.results_count -%}
                    {{ 'templates.search.results_with_count' | t: terms: search.terms, count: search.results_count }}
                  {%- elsif search.products_count == search.all_products_count -%}
                    {{ 'products.facets.product_count_simple' | t: count: search.products_count }}
                  {%- else -%}
                    {{
                      'products.facets.product_count'
                      | t: product_count: search.products_count, count: search.all_products_count
                    }}
                  {%- endif -%}
                </span>
              </h2>
              {%- render 'loading-spinner' -%}
            </div>
          </form>
        </facet-filters-form>
      {%- endif -%}
      <div
        {% if section.settings.filter_type == 'vertical' %}
          class="facets-vertical page-width"
        {% endif %}
      >
        {%- if search.filters != empty -%}
          {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
            <aside
              aria-labelledby="verticalTitle"
              class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}"
              id="main-search-filters"
              data-id="{{ section.id }}"
            >
              {% render 'facets',
                results: search,
                enable_filtering: section.settings.enable_filtering,
                enable_sorting: section.settings.enable_sorting,
                filter_type: section.settings.filter_type,
                paginate: paginate
              %}
            </aside>
          {%- endif -%}
        {%- endif -%}
        <div class="product-grid-container" id="ProductGridContainer">
          {%- if search.results.size == 0 and search.filters != empty -%}
            <div
              class="template-search__results collection collection--empty{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}"
              id="product-grid"
              data-id="{{ section.id }}"
            >
              <div class="loading-overlay gradient"></div>
              <div class="title-wrapper center">
                <h2 class="title title--primary">
                  {{ 'sections.collection_template.empty' | t -}}
                  <br>
                  {{
                    'sections.collection_template.use_fewer_filters_html'
                    | t: link: search_url, class: 'underlined-link link'
                  }}
                </h2>
              </div>
            </div>
          {%- else -%}
            <div
              class="template-search__results collection{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}"
              id="product-grid"
              data-id="{{ section.id }}"
            >
              <div class="loading-overlay gradient"></div>
              <ul
                class="grid product-grid  grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop"
                role="list"
              >
                {%- assign skip_card_product_styles = false -%}
                {%- for item in search.results -%}
                  {% assign lazy_load = false %}
                  {%- if forloop.index > 2 -%}
                    {%- assign lazy_load = true -%}
                  {%- endif -%}

                  <li
                    class="grid__item{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                    {% if settings.animations_reveal_on_scroll %}
                      data-cascade
                    {% endif %}
                  >
                    {%- case item.object_type -%}
                      {%- when 'product' -%}
                        {%- capture product_settings -%}{%- if section.settings.product_show_vendor -%}vendor,{%- endif -%}title,price{%- endcapture -%}
                        {% render 'card-product',
                          card_product: item,
                          media_aspect_ratio: section.settings.image_ratio,
                          image_shape: section.settings.image_shape,
                          show_secondary_image: section.settings.show_secondary_image,
                          show_vendor: section.settings.show_vendor,
                          show_rating: section.settings.show_rating,
                          lazy_load: lazy_load,
                          skip_styles: skip_card_product_styles
                        %}
                        {%- assign skip_card_product_styles = true -%}
                      {%- when 'article' -%}
                        {% render 'article-card',
                          article: item,
                          show_image: true,
                          show_date: section.settings.article_show_date,
                          show_author: section.settings.article_show_author,
                          show_badge: true,
                          media_aspect_ratio: 1,
                          lazy_load: lazy_load
                        %}
                      {%- when 'page' -%}
                        <div class="article-card-wrapper card-wrapper underline-links-hover">
                          <div
                            class="card card--card card--text ratio color-{{ settings.blog_card_color_scheme }}"
                            style="--ratio-percent: 100%;"
                          >
                            <div class="card__content">
                              <div class="card__information">
                                <h3 class="card__heading">
                                  <a href="{{ item.url }}" class="full-unstyled-link">
                                    {{ item.title | truncate: 50 | escape }}
                                  </a>
                                </h3>
                              </div>
                              <div class="card__badge {{ settings.badge_position }}">
                                <span class="badge color-{{ settings.color_schemes | first }}">
                                  {{- 'templates.search.page' | t -}}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                    {%- endcase -%}
                  </li>
                {%- endfor -%}
              </ul>
              {%- if paginate.pages > 1 -%}
                {% render 'pagination', paginate: paginate %}
              {%- endif -%}
            </div>
          {%- endif -%}
        </div>
      </div>
    {%- endif -%}
  </div>
{% endpaginate %}
{% if section.settings.image_shape == 'arch' %}
  {{ 'mask-arch.svg' | inline_asset_content }}
{%- endif -%}

{% schema %}
{
  "name": "t:sections.main-search.name",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "t:sections.main-search.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.main-search.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.main-search.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.main-search.settings.columns_mobile.options__2.label"
        }
      ]
    },    
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header__1.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.main-search.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.main-search.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.main-search.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.main-search.settings.image_ratio.label"
    },
    {
      "type": "select",
      "id": "image_shape",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.image_shape.options__1.label"
        },
        {
          "value": "arch",
          "label": "t:sections.all.image_shape.options__2.label"
        },
        {
          "value": "blob",
          "label": "t:sections.all.image_shape.options__3.label"
        },
        {
          "value": "chevronleft",
          "label": "t:sections.all.image_shape.options__4.label"
        },
        {
          "value": "chevronright",
          "label": "t:sections.all.image_shape.options__5.label"
        },
        {
          "value": "diamond",
          "label": "t:sections.all.image_shape.options__6.label"
        },
        {
          "value": "parallelogram",
          "label": "t:sections.all.image_shape.options__7.label"
        },
        {
          "value": "round",
          "label": "t:sections.all.image_shape.options__8.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.image_shape.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.main-search.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.main-search.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.main-search.settings.show_rating.label",
      "info": "t:sections.main-search.settings.show_rating.info"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_filtering.label",
      "info": "t:sections.main-collection-product-grid.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__1.label"
        },
        {
          "value": "vertical",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__3.label"
        }
      ],
      "default": "horizontal",
      "label": "t:sections.main-collection-product-grid.settings.filter_type.label"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_sorting.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header__2.content"
    },
    {
      "type": "checkbox",
      "id": "article_show_date",
      "default": true,
      "label": "t:sections.main-search.settings.article_show_date.label"
    },
    {
      "type": "checkbox",
      "id": "article_show_author",
      "default": false,
      "label": "t:sections.main-search.settings.article_show_author.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
