{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'quick-order-list.css' | asset_url | stylesheet_tag }}
{{ 'quantity-popover.css' | asset_url | stylesheet_tag }}

<script src="{{ 'quantity-popover.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'quick-order-list.js' | asset_url }}" defer="defer"></script>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{% render 'quick-order-list',
  product: product,
  show_image: section.settings.show_image,
  show_sku: section.settings.show_sku
%}

{% schema %}
{
  "name": "t:sections.quick-order-list.name",
  "limit": 1,
  "enabled_on": {
    "templates": ["product"]
  },
  "settings": [
    {
      "type": "range",
      "id": "variants_per_page",
      "min": 5,
      "max": 50,
      "step": 1,
      "default": 50,
      "label": "t:sections.quick-order-list.settings.variants_per_page.label"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": false,
      "label": "t:sections.quick-order-list.settings.show_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_sku",
      "default": false,
      "label": "t:sections.quick-order-list.settings.show_sku.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "t:sections.quick-order-list.presets.name"
    }
  ]
}
{% endschema %}
